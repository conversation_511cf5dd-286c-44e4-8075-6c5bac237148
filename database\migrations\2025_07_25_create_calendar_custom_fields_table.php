<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('calendar_custom_fields', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('field_type')->default('text'); // text, email, phone, textarea, select, etc.
            $table->boolean('is_required')->default(false);
            $table->boolean('is_default')->default(false); // for Name, Email, Phone default fields
            $table->json('options')->nullable(); // for select/radio options
            $table->text('description')->nullable();
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->integer('created_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('calendar_custom_fields');
    }
};
