<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CalendarCustomField extends Model
{
    protected $fillable = [
        'name',
        'field_type',
        'is_required',
        'is_default',
        'options',
        'description',
        'sort_order',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'options' => 'array',
        'is_required' => 'boolean',
        'is_default' => 'boolean',
        'is_active' => 'boolean',
    ];

    public static $fieldTypes = [
        'text' => 'Text',
        'email' => 'Email',
        'phone' => 'Phone',
        'textarea' => 'Textarea',
        'select' => 'Dropdown',
        'radio' => 'Radio Button',
        'checkbox' => 'Checkbox',
        'date' => 'Date',
        'number' => 'Number',
    ];

    /**
     * Get the user who created this custom field
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to get active fields only
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get fields ordered by sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at');
    }

    /**
     * Scope to get default fields (Name, Email, Phone)
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope to get custom (non-default) fields
     */
    public function scopeCustom($query)
    {
        return $query->where('is_default', false);
    }
}
