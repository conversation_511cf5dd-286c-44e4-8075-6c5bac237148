<?php

namespace App\Http\Controllers;

use App\Models\CalendarCustomField;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class CalendarCustomFieldController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $customFields = CalendarCustomField::where('created_by', Auth::user()->creatorId())
            ->ordered()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $customFields
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'field_type' => 'required|string|in:text,email,phone,textarea,select,radio,checkbox,date,number',
            'is_required' => 'boolean',
            'options' => 'nullable|array',
            'description' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $customField = CalendarCustomField::create([
                'name' => $request->name,
                'field_type' => $request->field_type,
                'is_required' => $request->is_required ?? false,
                'is_default' => false, // User-created fields are never default
                'options' => $request->options,
                'description' => $request->description,
                'sort_order' => CalendarCustomField::where('created_by', Auth::user()->creatorId())->max('sort_order') + 1,
                'is_active' => true,
                'created_by' => Auth::user()->creatorId(),
            ]);

            return response()->json([
                'success' => true,
                'message' => __('Custom field created successfully.'),
                'data' => $customField
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Error creating custom field: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'field_type' => 'required|string|in:text,email,phone,textarea,select,radio,checkbox,date,number',
            'is_required' => 'boolean',
            'options' => 'nullable|array',
            'description' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $customField = CalendarCustomField::where('created_by', Auth::user()->creatorId())
                ->findOrFail($id);

            $customField->update([
                'name' => $request->name,
                'field_type' => $request->field_type,
                'is_required' => $request->is_required ?? false,
                'options' => $request->options,
                'description' => $request->description,
            ]);

            return response()->json([
                'success' => true,
                'message' => __('Custom field updated successfully.'),
                'data' => $customField
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Error updating custom field: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            $customField = CalendarCustomField::where('created_by', Auth::user()->creatorId())
                ->findOrFail($id);

            // Don't allow deletion of default fields
            if ($customField->is_default) {
                return response()->json([
                    'success' => false,
                    'message' => __('Cannot delete default fields.')
                ], 403);
            }

            $customField->delete();

            return response()->json([
                'success' => true,
                'message' => __('Custom field deleted successfully.')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Error deleting custom field: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle the required status of a custom field
     */
    public function toggleRequired($id)
    {
        try {
            $customField = CalendarCustomField::where('created_by', Auth::user()->creatorId())
                ->findOrFail($id);

            $customField->update([
                'is_required' => !$customField->is_required
            ]);

            return response()->json([
                'success' => true,
                'message' => __('Field requirement updated successfully.'),
                'data' => $customField
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Error updating field requirement: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get active custom fields for dropdown
     */
    public function getActiveFields()
    {
        $customFields = CalendarCustomField::where('created_by', Auth::user()->creatorId())
            ->active()
            ->ordered()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $customFields
        ]);
    }
}
